/**
 * Test the booking confirmation API with the fixed WhatsApp service
 */

// Check if fetch is available (for Node.js environments)
if (typeof fetch === 'undefined') {
  // Try to import node-fetch for Node.js
  try {
    const { default: fetch } = await import('node-fetch');
    global.fetch = fetch;
  } catch (e) {
    console.error('❌ fetch is not available. Please install node-fetch or run in a browser environment.');
    process.exit(1);
  }
}

async function checkServerHealth() {
  const maxRetries = 10;
  const retryDelay = 3000; // 3 seconds

  for (let i = 0; i < maxRetries; i++) {
    try {
      console.log(`🔍 Checking server health (attempt ${i + 1}/${maxRetries})...`);

      // Try the WhatsApp health endpoint first
      const response = await fetch('http://localhost:3000/api/whatsapp/health', {
        method: 'GET'
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Server is running and healthy!');
        console.log('📱 WhatsApp service status:', result.healthy ? 'Healthy' : 'Unhealthy');
        return true;
      }
    } catch (error) {
      console.log(`⏳ Server not ready yet (${error.message}), retrying in ${retryDelay/1000}s...`);
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }

  console.log('❌ Server is not responding. Please start the Next.js server with: npm run dev');
  console.log('💡 Make sure to run: npm run dev in another terminal');
  return false;
}

async function testBookingAPI() {
  console.log('🧪 Testing booking confirmation API...');

  // Check if server is running first
  const serverHealthy = await checkServerHealth();
  if (!serverHealthy) {
    console.log('💡 To start the server, run: npm run dev');
    return;
  }

  // Sample booking data for testing
  const testBookingData = {
    bookingId: 12345,
    bookingRef: 'B0012345',
    parentName: 'Test Parent',
    parentPhone: '', // Your test number
    childName: 'Test Child',
    eventTitle: 'Birthday Party Celebration',
    eventDate: '2024-01-15',
    eventVenue: 'NIBOG Party Hall, Bangalore',
    totalAmount: 2500,
    paymentMethod: 'PhonePe',
    transactionId: 'TXN_TEST_001',
    gameDetails: [
      {
        gameName: 'Balloon Popping',
        gameTime: '10:00 AM',
        gamePrice: 500
      },
      {
        gameName: 'Musical Chairs',
        gameTime: '11:00 AM',
        gamePrice: 300
      },
      {
        gameName: 'Treasure Hunt',
        gameTime: '12:00 PM',
        gamePrice: 700
      }
    ],
    addOns: [
      {
        name: 'Birthday Cake',
        quantity: 1,
        price: 800
      },
      {
        name: 'Party Decorations',
        quantity: 1,
        price: 200
      }
    ]
  };

  try {
    console.log('📱 Sending test booking confirmation...');
    console.log('📞 Target phone:', testBookingData.parentPhone);
    console.log('🎉 Event:', testBookingData.eventTitle);

    // Call the booking confirmation API endpoint
    const response = await fetch('http://localhost:3000/api/whatsapp/send-booking-confirmation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testBookingData),
    });

    const result = await response.json();

    console.log('📡 API Response Status:', response.status);
    console.log('📡 API Response:', JSON.stringify(result, null, 2));

    if (response.ok && result.success) {
      console.log('✅ Booking confirmation API - SUCCESS!');
      console.log('📨 Message ID:', result.messageId);
      console.log('📱 Check your WhatsApp for the booking confirmation message');
      console.log('🎯 This should work without parameter mismatch errors!');
    } else {
      console.error('❌ Booking confirmation API - FAILED');
      console.error('Error:', result.error);
      if (result.zaptraResponse) {
        console.error('Zaptra Response:', JSON.stringify(result.zaptraResponse, null, 2));
      }
    }

  } catch (error) {
    console.error('🚨 Test failed with error:', error.message);
    console.error('💡 Make sure the Next.js server is running on http://localhost:3000');
  }
}

// Run the test
testBookingAPI();
